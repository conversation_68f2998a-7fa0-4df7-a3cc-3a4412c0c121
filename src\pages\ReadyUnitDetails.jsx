import { use<PERSON><PERSON><PERSON>, <PERSON> } from "react-router-dom";
import { useEffect, useState } from "react";
import axios from "axios";
import FooterWithEmail from "../layout/FooterWithEmail";

// Import ready property components
import ReadyHero from "../features/readyPropertyDetails/components/ReadyHero";
import ReadyGallery from "../features/readyPropertyDetails/components/ReadyGallery";
import ReadyMainInfo from "../features/readyPropertyDetails/components/ReadyMainInfo";
import ReadyPropertyFeatures from "../features/readyPropertyDetails/components/ReadyPropertyFeatures";
import ReadyNearby from "../features/readyPropertyDetails/components/ReadyNearby";
import ReadyAmenities from "../features/readyPropertyDetails/components/ReadyAmenities";

function ReadyUnitDetails() {
  const { id } = useParams();
  const [property, setProperty] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchProperty = async () => {
      setLoading(true);
      try {
        // TODO: Replace with actual ready property API endpoint when available
        const response = await axios.get(
          `${import.meta.env.VITE_API_BASE_URL}/api/ready-properties/${id}`
        );
        setProperty(response.data);
      } catch (error) {
        console.error("Failed to fetch ready property:", error);
        // For now, use mock data based on the provided JSON
        setProperty({
          "title": "Resale | 2 BR plus Maid | Zero Premium | 10%DP",
          "destination": {
            "_id": "6892038c189c487e611285ae",
            "name": "Dubai, UAE",
            "img": "experimental/destinations/6892038c189c487e611285ae/img/1754406314675-d9mrbh-1st.webp",
            "from": 481100
          },
          "type": "resale",
          "desc": "Like the description in the sample big-ass",
          "price": 2343150,
          "developer": "whatsoever",
          "project": "Muheira",
          "amenities": [
            "Maids Room",
            "Central A/C",
            "Balcony",
            "Shared Pool",
            "Concierge",
            "Covered Parking",
            "Built in Wardrobes",
            "Walk-in Closet",
            "Kitchen Appliances",
            "View of Landmark"
          ],
          "location": {
            "city": "Abu Dhabi",
            "area": { "name": "Al Reem Island" },
            "nearby": [
              "5 mins to Reem Mall",
              "15 mins to Downtown Abu Dhabi",
              "20 mins to Zayed International Airport",
              "15 mins to New York University"
            ]
          },
          "createdAt": "2024-11-01T17:43:24.480+00:00",
          "property_features": [
            { "label": "Property Type", "value": "Apartment", "type": "string" },
            { "label": "Bedrooms", "value": 4, "type": "number" },
            { "label": "Bathrooms", "value": 2, "type": "number" },
            { "label": "Size (sqft)", "value": 1431, "type": "number" },
            { "label": "Parking", "value": 1, "type": "number" },
            { "label": "Furnished", "value": true, "type": "boolean" },
            { "label": "Estimated Handover Date", "value": "Q4 2025", "type": "string" }
          ],
          "mainImage": "experimental/properties/resale/muheira-apartment/main.webp",
          "gallery": [
            "experimental/properties/resale/muheira-apartment/gallery/living-room.webp",
            "experimental/properties/resale/muheira-apartment/gallery/bedroom.webp",
            "experimental/properties/resale/muheira-apartment/gallery/kitchen.webp",
            "experimental/properties/resale/muheira-apartment/gallery/balcony.webp"
          ],
          "isActive": true,
          "isFeatured": false
        });
      } finally {
        setLoading(false);
      }
    };
    fetchProperty();
  }, [id]);

  if (loading)
    return (
      <div className="min-h-screen bg-[#EBE6E2] flex items-center justify-center pt-55">
        <div className="flex flex-col items-center">
          <div className="w-16 h-16 border-8 border-primary border-t-transparent rounded-full animate-spin mb-4" />
          <div className="text-2xl font-semibold text-primary animate-pulse">
            Loading property details...
          </div>
        </div>
      </div>
    );

  if (!property)
    return (
      <div className="min-h-screen bg-[#EBE6E2] p-10 pt-55">
        <div className="max-w-5xl mx-auto">
          <Link
            to="/destinations"
            className="text-primary hover:underline mb-8 inline-block"
          >
            ← Back to properties
          </Link>
          <div className="text-3xl">Property not found</div>
        </div>
      </div>
    );

  return (
    <div className="min-h-screen bg-[#EBE6E2] pt-55">
      {/* Hero Section */}
      <ReadyHero property={property} />

      <div className="mx-auto px-4 md:px-8 py-10 max-w-7xl ">
        <Link
          to="/destinations"
          className="text-primary hover:underline mb-8 inline-block text-lg font-medium"
        >
          ← Back to properties
        </Link>

        {/* Gallery and Main Info Section */}
        {property.gallery && property.gallery.length > 0 && (
          <section className="mb-12 space-y-8">
            <ReadyGallery property={property} />
            <ReadyMainInfo property={property} />
          </section>
        )}

        {/* Property Features and Amenities Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
          {/* Property Features - Left */}
          <div className="lg:order-1 h-full">
            <ReadyPropertyFeatures property={property} />
          </div>

          {/* Amenities - Right */}
          <div className="lg:order-2 h-full">
            <ReadyAmenities property={property} />
          </div>
        </div>

        {/* Location & Nearby Section */}
        <ReadyNearby property={property} />
      </div>

      <FooterWithEmail />
    </div>
  );
}

export default ReadyUnitDetails;
