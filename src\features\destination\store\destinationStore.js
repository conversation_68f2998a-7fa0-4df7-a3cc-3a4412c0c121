// store/destinationStore.js
import { create } from "zustand";

// const isObjectId = (v) => typeof v === "string" && /^[0-9a-fA-F]{24}$/.test(v);

const useProjectStore = create((set) => ({
  projects: [],
  filters: {
    destinations: [],
    types: [],
    developer: null,
  },
  loading: false,
  error: null,

  setFilters: (filters) => set({ filters }),

  fetchProjects: async (filters = {}, pageSize) => {
  set({
    loading: true,
    error: null,
    filters,
  });

  try {
    const filterConstructor = {};

    // DESTINATION (single ID)
    if (
      Array.isArray(filters.destinations) &&
      filters.destinations.length > 0 &&
      filters.destinations[0] !== "all"
    ) {
      // take the first selected ID
      filterConstructor.destination = filters.destinations[0];
    }

    // TYPES
    if (
      Array.isArray(filters.types) &&
      filters.types.length > 0 &&
      filters.types[0] !== "ready"
    ) {
      filterConstructor.types = filters.types;
    }

    // DEVELOPER
    if (filters.developer && filters.developer !== "all") {
      filterConstructor.developer = filters.developer;
    }

    let url = `${import.meta.env.VITE_API_BASE_URL}/api/projects/get`;
    if (pageSize) {
      url += `?pageSize=${pageSize}`;
    }

    console.debug("fetchProjects: POST body:", filterConstructor);

    const response = await fetch(url, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(filterConstructor),
    });

    if (!response.ok) {
      throw new Error(`Server error: ${response.status}`);
    }

    const data = await response.json();

    set({
      projects: data.projects || [],
      loading: false,
    });
  } catch (error) {
    console.error("❌ API Error:", error);
    set({
      error: error.message,
      loading: false,
    });
  }
},
}));

export default useProjectStore;
