import { FiMapPin, <PERSON><PERSON>lock, FiNavigation } from "react-icons/fi";

function ReadyNearby({ property }) {
  if (!property?.location?.nearby || property.location.nearby.length === 0) return null;

  return (
    <section className="mb-10">
      <div className="bg-white rounded-2xl shadow-xl p-8">
        {/* Title */}
        <div className="flex items-center gap-3 mb-6">
          <FiMapPin className="text-3xl text-primary" />
          <h3 className="text-3xl font-bold text-primary">Location & Nearby</h3>
        </div>

        {/* Location Info */}
        <div className="mb-8 p-6 bg-gradient-to-r from-primary/10 to-primary/5 rounded-xl">
          <div className="flex items-center gap-3 mb-3">
            <FiNavigation className="text-2xl text-primary" />
            <h4 className="text-xl font-bold text-gray-800">Property Location</h4>
          </div>
          <p className="text-lg text-gray-700 font-medium">
            {property.location.area?.name}, {property.location.city}
          </p>
        </div>

        {/* Nearby Places */}
        <div>
          <div className="flex items-center gap-3 mb-4">
            <FiClock className="text-2xl text-primary" />
            <h4 className="text-xl font-bold text-gray-800">Nearby Attractions</h4>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {property.location.nearby.map((place, index) => (
              <div
                key={index}
                className="flex items-center gap-3 bg-gray-50 rounded-lg px-4 py-3 hover:bg-gray-100 transition-colors"
              >
                <FiMapPin className="text-primary text-lg flex-shrink-0" />
                <span className="text-gray-700 font-medium text-base">
                  {place}
                </span>
              </div>
            ))}
          </div>
        </div>

        {/* Additional Info */}
        <div className="mt-6 pt-4 border-t border-gray-100">
          <p className="text-center text-gray-600 font-medium">
            Strategically located with easy access to key destinations
          </p>
        </div>
      </div>
    </section>
  );
}

export default ReadyNearby;
