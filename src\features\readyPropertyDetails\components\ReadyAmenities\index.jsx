import { FiStar, FiCheck } from "react-icons/fi";

function ReadyAmenities({ property }) {
  if (!property?.amenities || property.amenities.length === 0) return null;

  return (
    <section className="mb-10 h-full">
      <div className="bg-white rounded-2xl shadow-xl p-8 h-full">
        {/* Title */}
        <div className="flex items-center gap-3 mb-6">
          <FiStar className="text-3xl text-primary" />
          <h3 className="text-3xl font-bold text-primary">Amenities</h3>
        </div>

        {/* Amenities Grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
          {property.amenities.map((amenity, index) => (
            <div
              key={index}
              className="flex items-center gap-3 bg-gray-50 rounded-lg px-4 py-3 hover:bg-gray-100 transition-colors"
            >
              <FiCheck className="text-green-500 text-lg flex-shrink-0" />
              <span className="text-gray-700 font-medium text-base">
                {amenity}
              </span>
            </div>
          ))}
        </div>

        {/* Amenities count */}
        <div className="mt-6 pt-4 border-t border-gray-100">
          <p className="text-center text-gray-600 font-medium">
            <span className="text-primary font-bold text-lg">{property.amenities.length}</span> premium amenities included
          </p>
        </div>
      </div>
    </section>
  );
}

export default ReadyAmenities;
