import {
  FiCheckCircle,
  FiHome,
  FiCalendar,
  FiMapPin,
  FiSquare,
  // FiCar,
  FiCheck,
  FiX,
} from "react-icons/fi";

function ReadyPropertyFeatures({ property }) {
  if (!property) return null;

  // Helper function to render feature value based on type
  const renderFeatureValue = (feature) => {
    switch (feature.type) {
      case "boolean":
        return (
          <div className="flex items-center gap-2">
            {feature.value ? (
              <>
                <FiCheck className="text-green-500 text-lg" />
                <span className="text-green-600 font-semibold">Yes</span>
              </>
            ) : (
              <>
                <FiX className="text-red-500 text-lg" />
                <span className="text-red-600 font-semibold">No</span>
              </>
            )}
          </div>
        );
      case "number":
        return <span className="text-primary font-semibold text-lg">{feature.value}</span>;
      default:
        return <span className="text-primary font-semibold text-lg">{feature.value}</span>;
    }
  };

  // Helper function to get appropriate icon for feature
  const getFeatureIcon = (label) => {
    const iconMap = {
      "Property Type": <FiHome className="text-primary text-2xl" />,
      "Bedrooms": <FiHome className="text-primary text-2xl" />,
      "Bathrooms": <FiHome className="text-primary text-2xl" />,
      "Size (sqft)": <FiSquare className="text-primary text-2xl" />,
      // "Parking": <FiCar className="text-primary text-2xl" />,
      "Furnished": <FiCheck className="text-primary text-2xl" />,
      "Estimated Handover Date": <FiCalendar className="text-primary text-2xl" />,
    };
    return iconMap[label] || <FiCheckCircle className="text-primary text-2xl" />;
  };

  return (
    <section className="mb-10 h-full">
      <div className="bg-white rounded-2xl shadow-xl p-8 h-full flex flex-col justify-between">
        {/* Title */}
        <div className="flex items-center gap-3 mb-6">
          <FiCheckCircle className="text-3xl text-primary" />
          <h3 className="text-3xl font-bold text-primary">Property Features</h3>
        </div>

        {/* Feature Grid */}
        <div className="space-y-4">
          {property.property_features?.map((feature, index) => (
            <div
              key={index}
              className="flex items-center gap-4 bg-gray-50 rounded-lg px-5 py-4"
            >
              <div className="w-8">{getFeatureIcon(feature.label)}</div>
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between w-full">
                <span className="font-medium text-gray-700 text-base sm:text-lg">
                  {feature.label}
                </span>
                <div className="mt-1 sm:mt-0">
                  {renderFeatureValue(feature)}
                </div>
              </div>
            </div>
          ))}

          {/* Additional property info */}
          {property.location && (
            <div className="flex items-center gap-4 bg-gray-50 rounded-lg px-5 py-4">
              <div className="w-8">
                <FiMapPin className="text-primary text-2xl" />
              </div>
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between w-full">
                <span className="font-medium text-gray-700 text-base sm:text-lg">
                  Location
                </span>
                <span className="text-primary font-semibold text-lg mt-1 sm:mt-0">
                  {property.location.area?.name}, {property.location.city}
                </span>
              </div>
            </div>
          )}

          {property.developer && (
            <div className="flex items-center gap-4 bg-gray-50 rounded-lg px-5 py-4">
              <div className="w-8">
                <FiHome className="text-primary text-2xl" />
              </div>
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between w-full">
                <span className="font-medium text-gray-700 text-base sm:text-lg">
                  Developer
                </span>
                <span className="text-primary font-semibold text-lg mt-1 sm:mt-0">
                  {property.developer}
                </span>
              </div>
            </div>
          )}
        </div>
      </div>
    </section>
  );
}

export default ReadyPropertyFeatures;
