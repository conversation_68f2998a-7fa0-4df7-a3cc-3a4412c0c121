import { FiHome, FiStar, FiInfo, FiDollarSign, Fi<PERSON>ser, FiMapPin } from "react-icons/fi";

function ReadyMainInfo({ property }) {
  if (!property) return null;

  return (
    <section className="bg-white rounded-2xl shadow-lg p-6 md:p-8 h-full flex flex-col justify-between">
      <div>
        {/* Title */}
        <div className="flex items-center gap-3 mb-4">
          <FiHome className="text-3xl text-primary" />
          <h1 className="text-3xl md:text-4xl font-extrabold text-gray-900">
            {property.title}
          </h1>
        </div>

        {/* Project */}
        {property.project && (
          <div className="flex items-center gap-3 mb-5">
            <FiStar className="text-2xl text-primary" />
            <p className="text-xl text-primary font-semibold italic">
              {property.project}
            </p>
          </div>
        )}

        {/* Developer */}
        {property.developer && (
          <div className="flex items-center gap-3 mb-5">
            <FiUser className="text-2xl text-primary" />
            <p className="text-lg text-gray-700">
              <span className="font-medium">Developer:</span> {property.developer}
            </p>
          </div>
        )}

        {/* Location */}
        {property.location && (
          <div className="flex items-center gap-3 mb-5">
            <FiMapPin className="text-2xl text-primary" />
            <p className="text-lg text-gray-700">
              <span className="font-medium">Location:</span> {property.location.area?.name}, {property.location.city}
            </p>
          </div>
        )}

        {/* Description */}
        {property.desc && (
          <div className="flex items-start gap-3 mb-6">
            <FiInfo className="text-xl text-primary mt-1" />
            <p className="text-base md:text-lg text-gray-700 leading-relaxed">
              {property.desc}
            </p>
          </div>
        )}

        {/* Price */}
        {property.price && (
          <div className="flex items-center gap-3 mb-6">
            <FiDollarSign className="text-3xl text-primary" />
            <div>
              <p className="text-sm text-gray-600 font-medium">Property Price</p>
              <p className="text-3xl font-extrabold text-primary">
                AED {property.price.toLocaleString()}
              </p>
            </div>
          </div>
        )}
      </div>

      {/* Tags */}
      <div className="mt-auto">
        <div className="flex flex-wrap gap-3 pt-4 border-t border-gray-100">
          {property.type && (
            <span className="inline-flex items-center gap-2 bg-primary text-white px-5 py-2 rounded-full text-sm md:text-base font-medium">
              <FiHome className="text-base" />
              {property.type}
            </span>
          )}

          {property.destination && (
            <span className="inline-flex items-center gap-2 bg-gray-600 text-white px-5 py-2 rounded-full text-sm md:text-base font-medium">
              <FiMapPin className="text-base" />
              {property.destination.name}
            </span>
          )}
        </div>
      </div>
    </section>
  );
}

export default ReadyMainInfo;
