import { BrowserRouter as Router, Routes, Route, useLocation } from "react-router-dom";
import Header from "./layout/Header/Header";
// import './App.css'
import Home from "./pages/Home";
import Destinations from "./pages/Destinations";
import About from "./pages/About";
import PropertyDetails from "./pages/OffPlanPropertyDetails";
import Contact from "./pages/Contact";
import { useEffect } from "react";
import ReadyUnitDetails from "./pages/ReadyUnitDetails";



function Services() {
  return (
    <div className="page">
      <h1>Our Services</h1>
      <p>Discover our investment solutions</p>
    </div>
  );
}

function ScrollToTop() {
  const { pathname } = useLocation();

  useEffect(() => {
    window.scrollTo(0, 0);
  }, [pathname]);

  return null;
}

function App() {
  return (
    <Router>
      <ScrollToTop />
      <div className="App" id="top">
        <main>
          <Routes>
            <Route
              path="/"
              element={
                <>
                  <Header />
                  <Home />
                </>
              }
            />
            <Route
              path="/about"
              element={
                <>
                  <Header isDark={true} />
                  <About />
                </>
              }
            />
            <Route
              path="/services"
              element={
                <>
                  <Header />
                  <Services />
                </>
              }
            />
            <Route
              path="/contact"
              element={
                <>
                  <Header />
                  <Contact />
                </>
              }
            />
            <Route
              path="/destinations"
              element={
                <>
                  <Header />
                  <Destinations />
                </>
              }
            />
            <Route
              path="/offPlan-Property/:id"
              element={
                <>
                  <Header />
                  <PropertyDetails />
                </>
              }
            />
            <Route
              path="/ready-Property/:id"
              element={
                <>
                  <Header />
                  <ReadyUnitDetails />
                </>
              }
            />
          </Routes>
        </main>
      </div>
    </Router>
  );
}

export default App;
