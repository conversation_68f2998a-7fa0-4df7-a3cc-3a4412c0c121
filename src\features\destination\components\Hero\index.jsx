import { useState, useEffect } from "react";
import { FiSearch, FiChevronDown } from "react-icons/fi";
import useProjectsStore from "../../store/destinationStore";
import { useLocation } from "react-router-dom";
import useDeveloperStore from "../../store/developerStore";

const heroImg = "/images/heroImage/destination.webp";

export default function Hero() {
  const { setFilters, fetchProjects } = useProjectsStore();
  const locationConfig = useLocation();
  const params = new URLSearchParams(locationConfig.search);

  const [projectType, setProjectType] = useState("ready");
  const [location, setLocation] = useState("all");
  const [developer, setDeveloper] = useState("all");

  const [destinations, setDestinations] = useState([]);

  const { developers, fetchDevelopers, loading, error } = useDeveloperStore();

  useEffect(() => {
    fetchDevelopers();
  }, [fetchDevelopers]);

  // Fetch destinations
  useEffect(() => {
    const fetchDestinations = async () => {
      try {
        const res = await fetch(
          `${import.meta.env.VITE_API_BASE_URL}/api/destinations?pageSize=100&pageNumber=1`
        );
        const data = await res.json();
        setDestinations(data.destinations || []);
      } catch (err) {
        console.error("Failed to fetch destinations:", err);
        setDestinations([]);
      }
    };
    fetchDestinations();
  }, []);

  // Sync filter state and search on URL query change
  useEffect(() => {
    const destFromUrl = params.get("destination") || "all";
    const devFromUrl = params.get("developer") || "all";
    const typeFromUrl = params.get("type") || "ready";
    setLocation(destFromUrl);
    setDeveloper(devFromUrl);
    setProjectType(typeFromUrl);

    const filters = {
      destinations: destFromUrl === "all" ? [] : [destFromUrl],
      types: [typeFromUrl],
      developer: devFromUrl === "all" ? null : devFromUrl,
    };

    setFilters(filters);
    fetchProjects(filters);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [locationConfig.search]);

  const handleSearch = () => {
    const newFilters = {
      destinations: location === "all" ? [] : [location],
      types: [projectType],
      developer: developer === "all" ? null : developer,
    };

    setFilters(newFilters);
    fetchProjects(newFilters);
  };

  return (
    <section className="w-full text-center h-screen flex flex-col pt-55">
      {/* Headings */}
      <div className="max-w-[1200px] mx-auto pt-12 px-4 flex-shrink-0">
        <h1 className="font-playfair text-[3.6rem] sm:text-[4.8rem] md:text-[6rem] leading-[1.1] font-medium tracking-[-0.04em]">
          Where Dubai Investment
          <br />
          <span className="italic text-[#D1A954]">Becomes Reality</span>
        </h1>
        <p className="font-outfit font-light text-base sm:text-lg md:text-2xl mt-6 max-w-[90rem] mx-auto">
          Strategically located <span className="font-semibold">projects</span>{" "}
          offering <span className="font-semibold">strong returns</span>,
          long-term growth.
        </p>
      </div>

      {/* Hero Image + Filter Bar */}
      <div className="relative mt-12 flex-1 min-h-0">
        <img
          src={heroImg}
          alt="Dubai Skyline"
          className="w-full h-full object-cover"
        />

        <div className="absolute inset-0 flex items-center justify-center">
          {/* Desktop Filter Bar */}
          <div className="hidden md:flex bg-[#FCFCFCAB] backdrop-blur-sm shadow-md rounded-full px-4 py-2 items-center justify-between h-20 w-10/12 md:h-24">
            {/* Project Type */}
            <div className="flex flex-col flex-1 relative text-start pl-10">
              <label className="font-outfit font-normal md:text-2xl text-xl text-[#1E1E1E] mb-1">
                Project Type
              </label>
              <div className="relative">
                <select
                  className="appearance-none font-outfit font-normal text-xl text-[#5E5E5E] bg-transparent w-full pr-6"
                  value={projectType}
                  onChange={(e) => setProjectType(e.target.value)}
                >
                  <option value="off-plan">Off Plan</option>
                  <option value="ready">Ready</option>
                </select>
                <FiChevronDown
                  className="absolute right-0 top-1/2 -translate-y-1/2 text-[#5E5E5E]"
                  size={16}
                />
              </div>
            </div>

            <div className="border-l h-3/4 mx-4" />

            {/* Location */}
            <div className="flex flex-col flex-1 relative text-start pl-10">
              <label className="font-outfit font-normal text-2xl text-[#1E1E1E] mb-1">
                Location
              </label>
              <div className="relative">
                <select
                  className="appearance-none font-outfit font-normal text-xl text-[#5E5E5E] bg-transparent w-full pr-6"
                  value={location}
                  onChange={(e) => setLocation(e.target.value)}
                >
                  <option value="all">All</option>
                  {destinations.map((dest) => (
                    <option key={dest._id} value={dest._id}>
                      {dest.name}
                    </option>
                  ))}
                </select>
                <FiChevronDown
                  className="absolute right-0 top-1/2 -translate-y-1/2 text-[#5E5E5E]"
                  size={16}
                />
              </div>
            </div>

            <div className="border-l h-3/4 mx-4" />

            {/* Developer */}
            <div className="flex flex-col flex-1 relative text-start pl-10">
              <label className="font-outfit font-normal text-2xl text-[#1E1E1E] mb-1">
                Developer :
              </label>
              <div className="relative">
                <select
                  className="appearance-none font-outfit font-normal text-xl text-[#5E5E5E] bg-transparent w-full pr-6"
                  value={developer}
                  onChange={(e) => setDeveloper(e.target.value)}
                >
                  <option value="all">All</option>
                  {developers.map((dev) => (
                    <option key={dev._id} value={dev._id}>
                      {dev.name}
                    </option>
                  ))}
                </select>
                <FiChevronDown
                  className="absolute right-0 top-1/2 -translate-y-1/2 text-[#5E5E5E]"
                  size={16}
                />
              </div>
            </div>

            <button
              onClick={handleSearch}
              className="bg-[#D1A954] text-white font-outfit w-56 h-20 rounded-full flex items-center justify-center gap-2 ml-4"
            >
              <FiSearch size={20} />
              Search
            </button>
          </div>

          {/* Mobile Filter Bar */}
          <div className="md:hidden bg-[#FCFCFCAB] backdrop-blur-sm shadow-md rounded-lg p-4 w-11/12 max-w-md flex flex-col gap-4">
            {/* Project Type */}
            <div className="flex flex-col text-start">
              <label className="font-outfit font-normal text-lg text-[#1E1E1E] mb-1">
                Project Type
              </label>
              <div className="relative">
                <select
                  className="appearance-none font-outfit font-normal text-base text-[#5E5E5E] bg-transparent w-full pr-6 py-2 border border-gray-300 rounded"
                  value={projectType}
                  onChange={(e) => setProjectType(e.target.value)}
                >
                  <option value="off-plan">Off Plan</option>
                  <option value="ready">Ready</option>
                </select>
                <FiChevronDown
                  className="absolute right-3 top-1/2 -translate-y-1/2 text-[#5E5E5E]"
                  size={16}
                />
              </div>
            </div>

            {/* Location */}
            <div className="flex flex-col text-start">
              <label className="font-outfit font-normal text-lg text-[#1E1E1E] mb-1">
                Location
              </label>
              <div className="relative">
                <select
                  className="appearance-none font-outfit font-normal text-base text-[#5E5E5E] bg-transparent w-full pr-6 py-2 border border-gray-300 rounded"
                  value={location}
                  onChange={(e) => setLocation(e.target.value)}
                >
                  <option value="all">All</option>
                  {destinations.map((dest) => (
                    <option key={dest._id} value={dest._id}>
                      {dest.name}
                    </option>
                  ))}
                </select>
                <FiChevronDown
                  className="absolute right-3 top-1/2 -translate-y-1/2 text-[#5E5E5E]"
                  size={16}
                />
              </div>
            </div>

            {/* Developer */}
            <div className="flex flex-col text-start">
              <label className="font-outfit font-normal text-lg text-[#1E1E1E] mb-1">
                Developer
              </label>
              <div className="relative">
                <select
                  className="appearance-none font-outfit font-normal text-base text-[#5E5E5E] bg-transparent w-full pr-6 py-2 border border-gray-300 rounded"
                  value={developer}
                  onChange={(e) => setDeveloper(e.target.value)}
                >
                  <option value="all">All</option>
                  {developers.map((dev) => (
                    <option key={dev._id} value={dev._id}>
                      {dev.name}
                    </option>
                  ))}
                </select>
                <FiChevronDown
                  className="absolute right-3 top-1/2 -translate-y-1/2 text-[#5E5E5E]"
                  size={16}
                />
              </div>
            </div>

            <button
              onClick={handleSearch}
              className="bg-[#D1A954] text-white font-outfit py-3 rounded-md flex items-center justify-center gap-2 mt-2"
            >
              <FiSearch size={20} />
              Search
            </button>
          </div>
        </div>
      </div>
    </section>
  );
}
